package seoclarity.actonia_competitor_summary.upload.politecrawl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import org.junit.Test;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.UrlMetricsEntity;

import java.util.Date;

import static org.junit.Assert.*;

public class CrawlerResponseTest {

    @Test
    public void testToUrlMetricsEntity() {
        // Create a sample CrawlerResponse
        CrawlerResponse crawlerResponse = new CrawlerResponse();
        
        // Set basic fields
        crawlerResponse.setCrawl_date_long(System.currentTimeMillis());
        crawlerResponse.setDomainId(123);
        crawlerResponse.setUrl("https://example.com");
        crawlerResponse.setUrlMurmurHash("test-hash");
        crawlerResponse.setBrowserMurmurHash("browser-hash");
        crawlerResponse.setSumMd5("sum-md5");
        crawlerResponse.setIndicatorsMd5Json("indicators-md5");
        crawlerResponse.setCrawlTimestamp(new Date());
        
        // Set some JSON fields
        JSONArray alternateLinks = new JSONArray();
        alternateLinks.add("link1");
        alternateLinks.add("link2");
        crawlerResponse.setAlternate_links(alternateLinks);
        
        JSONObject structuredData = new JSONObject();
        structuredData.put("type", "Article");
        structuredData.put("title", "Test Article");
        crawlerResponse.setStructured_data(structuredData);
        
        // Set Google Gson JsonObject
        JsonObject pageAnalysisRule = new JsonObject();
        pageAnalysisRule.addProperty("isIssue", true);
        pageAnalysisRule.addProperty("severity", "high");
        crawlerResponse.setPage_analysis_rule_1_b(pageAnalysisRule);
        
        // Set some string and integer fields
        crawlerResponse.setTitle("Test Title");
        crawlerResponse.setDescription("Test Description");
        crawlerResponse.setResponse_code(200);
        crawlerResponse.setTitle_length(10);
        
        // Convert to UrlMetricsEntity
        UrlMetricsEntity entity = crawlerResponse.toUrlMetricsEntity();
        
        // Verify basic fields
        assertNotNull(entity);
        assertEquals(Integer.valueOf(123), entity.getDomainId());
        assertEquals("https://example.com", entity.getUrl());
        assertEquals("test-hash", entity.getUrlMurmurHash());
        assertEquals("browser-hash", entity.getBrowserMurmurHash());
        assertEquals("sum-md5", entity.getSumMd5());
        assertEquals("indicators-md5", entity.getIndicatorsMd5Json());
        
        // Verify date conversion
        assertNotNull(entity.getCrawlDate());
        assertNotNull(entity.getCrawlTime());
        assertNotNull(entity.getCrawlTimestamp());
        
        // Verify string fields
        assertEquals("Test Title", entity.getTitle());
        assertEquals("Test Description", entity.getDescription());
        
        // Verify integer fields
        assertEquals(Integer.valueOf(200), entity.getResponse_code());
        assertEquals(Integer.valueOf(10), entity.getTitle_length());
        
        // Verify JSON fields converted to String using toJSONString()
        assertNotNull(entity.getAlternate_links());
        assertTrue(entity.getAlternate_links().contains("link1"));
        assertTrue(entity.getAlternate_links().contains("link2"));
        
        assertNotNull(entity.getStructured_data());
        assertTrue(entity.getStructured_data().contains("Article"));
        assertTrue(entity.getStructured_data().contains("Test Article"));
        
        // Verify Google Gson JsonObject converted to String
        assertNotNull(entity.getPage_analysis_results_chg_ind_json());
        assertTrue(entity.getPage_analysis_results_chg_ind_json().contains("isIssue"));
        assertTrue(entity.getPage_analysis_results_chg_ind_json().contains("true"));
        assertTrue(entity.getPage_analysis_results_chg_ind_json().contains("severity"));
        assertTrue(entity.getPage_analysis_results_chg_ind_json().contains("high"));
    }
    
    @Test
    public void testToUrlMetricsEntityWithNullValues() {
        // Test with minimal data
        CrawlerResponse crawlerResponse = new CrawlerResponse();
        crawlerResponse.setDomainId(456);
        crawlerResponse.setUrl("https://test.com");
        
        UrlMetricsEntity entity = crawlerResponse.toUrlMetricsEntity();
        
        assertNotNull(entity);
        assertEquals(Integer.valueOf(456), entity.getDomainId());
        assertEquals("https://test.com", entity.getUrl());
        
        // Verify null JSON fields don't cause issues
        assertNull(entity.getAlternate_links());
        assertNull(entity.getStructured_data());
        assertNull(entity.getPage_analysis_results_chg_ind_json());
    }
}
