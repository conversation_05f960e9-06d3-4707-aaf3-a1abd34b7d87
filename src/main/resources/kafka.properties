## RI Consumer Config
ri.consumer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
ri.consumer.kafka.bootstrap.ssl.servers=69.175.4.114:9094,69.175.4.150:9094,69.175.4.158:9094
ri.consumer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
#AvgSize: 60KB; Percentile50: 46KB; Percentile75: 84KB
ri.consumer.max.poll.records=200
# (AvgSize + 10KB) * (max.poll.records / 10) * 1024
ri.consumer.fetch.min.bytes=716800
# (AvgSize + 10KB) * max.poll.records * 1024
ri.consumer.fetch.max.bytes=14336000
# (AvgSize + 10KB) * max.poll.records / 4 * 1024
ri.consumer.max.partition.fetch.bytes=7168000
# max.wait.ms > Max IO / fetch.max.bytes
ri.consumer.fetch.max.wait.ms=3000
ri.consumer.session.timeout.ms=20000
ri.consumer.heartbeat.interval.ms=1000
ri.consumer.auto.commit=false
ri.consumer.auto.offset.reset=earliest

## RI regular consumer group
ri.daily.consumer.kafka.group.id=ri_consumer_group_regular_daily
ri.weekly.consumer.kafka.group.id=ri_consumer_group_regular_weekly
ri.biweekly.consumer.kafka.group.id=ri_consumer_group_regular_biweekly

## RI realtime consumer group
ri.realtime.daily.consumer.kafka.group.id=ri_consumer_group_realtime_daily
ri.realtime.weekly.consumer.kafka.group.id=ri_consumer_group_realtime_weekly
ri.realtime.biweekly.consumer.kafka.group.id=ri_consumer_group_realtime_biweekly

## RI mismatch consumer group
ri.daily.mismatch.consumer.kafka.group.id=ri_consumer_group_mismatch_daily
ri.weekly.mismatch.consumer.kafka.group.id=ri_consumer_group_mismatch_weekly
ri.biweekly.mismatch.consumer.kafka.group.id=ri_consumer_group_mismatch_biweekly

## RI back process consumer group
ri.daily.backProcess.consumer.kafka.group.id=ri_consumer_group_backProcess_daily
ri.weekly.backProcess.consumer.kafka.group.id=ri_consumer_group_backProcess_weekly
ri.biweekly.backProcess.consumer.kafka.group.id=ri_consumer_group_backProcess_biweekly

## Special Ranking consumer group
ads.consumer.kafka.group.id=spec_ads_consumer_group_regular
job.consumer.kafka.group.id=spec_job_consumer_group_regular
ll.consumer.kafka.group.id=spec_ll_consumer_group_regular
youtube.consumer.kafka.group.id=spec_youtube_consumer_group_regular

msg.info.consumer.kafka.group.id=ri_message_info_consumer_group_regular

## AIO content consumer group
ri.aio.content.kafka.consumer.group.id=ri_aio_content_consumer_group_regular
rg.aio.content.kafka.consumer.group.id=rg_aio_content_consumer_group_regular

## link seeker
cf.linkSeeker.kafka.consumer.group.id=cf_link_seeker_consumer_group_regular

rank.qcved.consumer.kafka.topic=ranking_qc_ved
rank.qcved.consumer.kafka.group.id=qcved_consumer_group

## RG Consumer config
rg.consumer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
rg.consumer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
rg.consumer.kafka.bootstrap.servers.backup=63.251.114.6:9093,63.251.114.11:9093,63.251.114.10:9093
rg.consumer.kafka.bootstrap.internal.servers.backup=172.27.2.7:9092,172.27.2.11:9092,172.27.2.10:9092
rg.consumer.max.poll.records=100
rg.consumer.fetch.min.bytes=1024000
#80KB(Avg+30)*100*1024
rg.consumer.fetch.max.bytes=8192000
#100/3(Partition)*50(Avg)*1024
rg.consumer.max.partition.fetch.bytes=1710000
rg.consumer.fetch.max.wait.ms=1500
rg.consumer.session.timeout.ms=20000
rg.consumer.heartbeat.interval.ms=1000
rg.consumer.auto.commit=false
rg.consumer.auto.offset.reset=earliest

## RG Consumer group
rg.consumer.kafka.group.id=rg_consumer_group_r1
rg.pla.consumer.kafka.group.id=rg_pla_consumer_group_r1
rg.reserved.consumer.kafka.group.id=rg_reserved_consumer_group_r1
rg.realtime.expansion.consumer.kafka.group.id=rg_realtime_consumer_group_r1
rg.live.search.consumer.kafka.group.id=rg_live_search_consumer_group_r1
rg.expansion.recommend.consumer.kafka.group.id=rg_expansion_recommend_consumer_group
rg.expansion.upload.consumer.kafka.group.id=rg_expansion_upload_consumer_group

## RI Producer Config
ri.producer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
ri.producer.kafka.bootstrap.ssl.servers=69.175.4.114:9094,69.175.4.150:9094,69.175.4.158:9094
ri.producer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
ri.producer.kafka.client.id=ri_producer_client_r1
ri.producer.kafka.acks=1
ri.producer.kafka.compression.type=none
ri.producer.kafka.buffer.memory=268435456
ri.producer.kafka.retries=2
ri.producer.kafka.retry.backoff.ms=100
ri.producer.kafka.batch.size=1048576
ri.producer.kafka.max.block.ms=100000
ri.producer.kafka.max.request.size=5242880
ri.producer.kafka.linger.ms=100
ri.producer.kafka.request.timeout.ms=1000000

## RI Backup Producer Config
ri.backup.producer.kafka.bootstrap.servers=backup-kfk-data-301:9093,backup-kfk-data-302:9093,backup-kfk-data-303:9093
ri.backup.producer.kafka.bootstrap.internal.servers=backup-kfk-data-301_internal:9092,backup-kfk-data-302_internal:9092,backup-kfk-data-303_internal:9092
#ri.backup.producer.kafka.bootstrap.servers=63.251.114.6:9093,63.251.114.11:9093,63.251.114.10:9093
#ri.backup.producer.kafka.bootstrap.internal.servers=172.27.2.7:9092,172.27.2.11:9092,172.27.2.10:9092
ri.backup.producer.kafka.client.id=backup_rank_producer_client
ri.backup.producer.kafka.acks=all
ri.backup.producer.kafka.compression.type=none
ri.backup.producer.kafka.buffer.memory=10485760
ri.backup.producer.kafka.retries=2
ri.backup.producer.kafka.retry.backoff.ms=100
ri.backup.producer.kafka.batch.size=1048576
ri.backup.producer.kafka.max.block.ms=600000
ri.backup.producer.kafka.max.request.size=5242880
ri.backup.producer.kafka.linger.ms=200
ri.backup.producer.kafka.request.timeout.ms=100000

## RG Producer Config
rg.producer.kafka.bootstrap.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
rg.producer.kafka.client.id=rg_producer_client_1
rg.producer.kafka.acks=all
rg.producer.kafka.compression.type=none
#rg.producer.kafka.buffer.memory=33554432
rg.producer.kafka.buffer.memory=268435456
rg.producer.kafka.retries=2
rg.producer.kafka.retry.backoff.ms=100
rg.producer.kafka.batch.size=1048576
rg.producer.kafka.max.block.ms=100000
rg.producer.kafka.max.request.size=5242880
rg.producer.kafka.linger.ms=100
rg.producer.kafka.request.timeout.ms=1000000

## Page Crawler Consumer Config
page.crawler.consumer.kafka.bootstrap.servers=10.5.34.4:9092,10.5.34.5:9092,10.5.34.6:9092
page.crawler.consumer.kafka.bootstrap.internal.servers=10.5.34.4:9092,10.5.34.5:9092,10.5.34.6:9092
page.crawler.consumer.kafka.group.id=url_crawl_consumer_group_prod
# 10 messages
page.crawler.consumer.max.poll.records=2000
# 2040 / 2
page.crawler.consumer.fetch.min.bytes=256000
# 10 * 170b * 1.2(æé)
page.crawler.consumer.fetch.max.bytes=1024000
# 2 * 170b * 1.2(æé)
page.crawler.consumer.max.partition.fetch.bytes=102400
page.crawler.consumer.fetch.max.wait.ms=3000
page.crawler.consumer.session.timeout.ms=20000
page.crawler.consumer.heartbeat.interval.ms=1000
page.crawler.consumer.auto.commit=false
page.crawler.consumer.auto.offset.reset=earliest


## Local Ved Consumer Config
local.ved.consumer.kafka.bootstrap.internal.servers=10.5.33.198:9092,10.5.33.199:9092,10.5.33.200:9092
local.ved.consumer.kafka.group.id=local_ved_qc_consumer_group
local.ved.consumer.max.poll.records=2000
local.ved.consumer.fetch.min.bytes=1024000
local.ved.consumer.fetch.max.bytes=131072000
local.ved.consumer.max.partition.fetch.bytes=6553600
local.ved.consumer.fetch.max.wait.ms=3000
local.ved.consumer.auto.commit=false
local.ved.consumer.auto.offset.reset=earliest

## Crawl Url Consumer Config
crawl.url.consumer.kafka.bootstrap.internal.servers=10.5.34.4:9092,10.5.34.5:9092,10.5.34.6:9092
crawl.url.consumer.kafka.group.id=crawl_url_consumer_group
crawl.url.consumer.max.poll.records=2000
crawl.url.consumer.fetch.min.bytes=102400
crawl.url.consumer.fetch.max.bytes=13107200
crawl.url.consumer.max.partition.fetch.bytes=655360
crawl.url.consumer.fetch.max.wait.ms=3000
crawl.url.consumer.auto.commit=false
crawl.url.consumer.auto.offset.reset=earliest

## Keyword Suggest Producer Config
keyword.suggest.producer.kafka.bootstrap.internal.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
keyword.suggest.producer.kafka.client.id=keyword_suggest_producer_client
keyword.suggest.producer.kafka.acks=1
keyword.suggest.producer.kafka.compression.type=none
keyword.suggest.producer.kafka.buffer.memory=268435456
keyword.suggest.producer.kafka.retries=2
keyword.suggest.producer.kafka.retry.backoff.ms=100
keyword.suggest.producer.kafka.batch.size=1048576
keyword.suggest.producer.kafka.max.block.ms=100000
keyword.suggest.producer.kafka.max.request.size=5242880
keyword.suggest.producer.kafka.linger.ms=100
keyword.suggest.producer.kafka.request.timeout.ms=1000000

## Keyword Suggest Consumer Config
keyword.suggest.consumer.kafka.bootstrap.internal.servers=10.5.33.198:9092,10.5.33.199:9092,10.5.33.200:9092
keyword.suggest.consumer.kafka.group.id=keyword_suggest_consumer_group
keyword.suggest.consumer.max.poll.records=2000
keyword.suggest.consumer.fetch.min.bytes=10240
keyword.suggest.consumer.fetch.max.bytes=1310720
keyword.suggest.consumer.max.partition.fetch.bytes=65536
keyword.suggest.consumer.fetch.max.wait.ms=3000
keyword.suggest.consumer.auto.commit=false
keyword.suggest.consumer.auto.offset.reset=earliest

## AdHoc Consumer Config
adhoc.consumer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
adhoc.consumer.kafka.group.id=adhoc_consumer_group_regular_hourly
# 10 messages
adhoc.consumer.max.poll.records=100
# 2040 / 2
adhoc.consumer.fetch.min.bytes=358400
# 10 * 170b * 1.2(æé)
adhoc.consumer.fetch.max.bytes=5529600
# 2 * 170b * 1.2(æé)
adhoc.consumer.max.partition.fetch.bytes=1382400
adhoc.consumer.fetch.max.wait.ms=2000
adhoc.consumer.session.timeout.ms=20000
adhoc.consumer.heartbeat.interval.ms=1000
adhoc.consumer.auto.commit=false
adhoc.consumer.auto.offset.reset=earliest

adhoc.ads.consumer.kafka.group.id=adhoc_ads_consumer_group_prod
adhoc.ranking.consumer.kafka.group.id=adhoc_ranking_consumer_group_prod
adhoc.aio.ranking.consumer.kafka.group.id=adhoc_aio_consumer_group_prod


## Special Rank Consumer Config
specRank.consumer.kafka.bootstrap.servers=69.175.4.114:9092,69.175.4.150:9092,69.175.4.158:9092
specRank.consumer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
specRank.consumer.max.poll.records=50
specRank.consumer.fetch.min.bytes=512000
specRank.consumer.fetch.max.bytes=3072000
specRank.consumer.max.partition.fetch.bytes=1536000
specRank.consumer.fetch.max.wait.ms=1000
specRank.consumer.session.timeout.ms=20000
specRank.consumer.heartbeat.interval.ms=1000
specRank.consumer.auto.commit=false
specRank.consumer.auto.offset.reset=earliest

## AIO Content Consumer Config
## RG AVG 49KB, RI AVG 38KB
aio.content.consumer.kafka.bootstrap.servers=69.175.4.114:9092,69.175.4.150:9092,69.175.4.158:9092
aio.content.consumer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
aio.content.consumer.max.poll.records=50
aio.content.consumer.fetch.min.bytes=512000
aio.content.consumer.fetch.max.bytes=3072000
aio.content.consumer.max.partition.fetch.bytes=1536000
aio.content.consumer.fetch.max.wait.ms=1000
aio.content.consumer.session.timeout.ms=20000
aio.content.consumer.heartbeat.interval.ms=1000
aio.content.consumer.auto.commit=false
aio.content.consumer.auto.offset.reset=earliest

## Cloud Flare Link Seeker Consumer Config
cf.linkSeeker.consumer.kafka.bootstrap.servers=173.236.57.146:9093:9093,65.60.14.78:9093:9093,65.60.14.134:9093:9093
cf.linkSeeker.consumer.kafka.bootstrap.internal.servers=10.5.34.4:9092,10.5.34.5:9092,10.5.34.6:9092
cf.linkSeeker.consumer.max.poll.records=100
cf.linkSeeker.consumer.fetch.min.bytes=512000
cf.linkSeeker.consumer.fetch.max.bytes=3072000
cf.linkSeeker.consumer.max.partition.fetch.bytes=1536000
cf.linkSeeker.consumer.fetch.max.wait.ms=1000
cf.linkSeeker.consumer.session.timeout.ms=20000
cf.linkSeeker.consumer.heartbeat.interval.ms=1000
cf.linkSeeker.consumer.auto.commit=false
cf.linkSeeker.consumer.auto.offset.reset=earliest

## Rank Kafka Admin Config
rank.admin.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
rank.admin.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
rank.admin.kafka.group.id=daily-ranking-consumer-03
rank.admin.session.timeout.ms=30000
rank.admin.heartbeat.interval.ms=5000
rank.admin.auto.commit=false
rank.admin.auto.offset.reset=earliest

## Rank Kafka JMX
kafka.jmx.service.url=service:jmx:rmi:///jndi/rmi://10.5.33.200:9999/jmxrmi


## market Consumer Config
market.consumer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
market.consumer.kafka.group.id=market_consumer_group
market.consumer.max.poll.records=10
market.consumer.fetch.min.bytes=1020
market.consumer.fetch.max.bytes=2040
market.consumer.max.partition.fetch.bytes=408
market.consumer.fetch.max.wait.ms=3000
market.consumer.session.timeout.ms=20000
market.consumer.heartbeat.interval.ms=1000
market.consumer.auto.commit=false
market.consumer.auto.offset.reset=earliest

## google trend Consumer Config
google.trend.consumer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
google.trend.consumer.kafka.group.id=google_trend_consumer_group
google.trend.consumer.max.poll.records=10
google.trend.consumer.fetch.min.bytes=1020
google.trend.consumer.fetch.max.bytes=2040
google.trend.consumer.max.partition.fetch.bytes=408
google.trend.consumer.fetch.max.wait.ms=3000
google.trend.consumer.session.timeout.ms=20000
google.trend.consumer.heartbeat.interval.ms=1000
google.trend.consumer.auto.commit=false
google.trend.consumer.auto.offset.reset=earliest

## Ai Scraper Consumer Config
#ai.scraper.consumer.kafka.bootstrap.internal.servers=10.5.33.198:9092,10.5.33.199:9092,10.5.33.200:9092
ai.scraper.consumer.kafka.bootstrap.internal.servers=173.236.57.146:9093,65.60.14.78:9093,65.60.14.134:9093
ai.scraper.consumer.kafka.group.id=ai_scraper_consumer_group_prod
ai.scraper.consumer.max.poll.records=2000
ai.scraper.consumer.fetch.min.bytes=1024000
ai.scraper.consumer.fetch.max.bytes=131072000
ai.scraper.consumer.max.partition.fetch.bytes=6553600
ai.scraper.consumer.fetch.max.wait.ms=3000
ai.scraper.consumer.auto.commit=false
ai.scraper.consumer.auto.offset.reset=earliest

## Url Inspection Consumer Config
url.inspection.consumer.kafka.bootstrap.internal.servers=10.5.34.4:9092,10.5.34.5:9092,10.5.34.6:9092
url.inspection.consumer.kafka.group.id=url_inspection_consumer_group_prod
url.inspection.consumer.max.poll.records=2000
url.inspection.consumer.fetch.min.bytes=1024000
url.inspection.consumer.fetch.max.bytes=131072000
url.inspection.consumer.max.partition.fetch.bytes=6553600
url.inspection.consumer.fetch.max.wait.ms=3000
url.inspection.consumer.auto.commit=false
url.inspection.consumer.auto.offset.reset=earliest

pixelV3.consumer.kafka.bootstrap.internal.servers=backup-kfk-data-301:9093,backup-kfk-data-302:9093,backup-kfk-data-303:9093
pixelV3.consumer.kafka.group.id=pixelV3_consumer_prod
pixelV3.consumer.max.poll.records=1000
pixelV3.consumer.fetch.min.bytes=716800
pixelV3.consumer.fetch.max.bytes=14336000
pixelV3.consumer.max.partition.fetch.bytes=7168000
pixelV3.consumer.fetch.max.wait.ms=3000
pixelV3.consumer.auto.commit=false
pixelV3.consumer.auto.offset.reset=earliest

## Polite Crawl Consumer Config
#polite.crawl.consumer.kafka.bootstrap.internal.servers=10.5.33.198:9092,10.5.33.199:9092,10.5.33.200:9092
polite.crawl.consumer.kafka.bootstrap.internal.servers=173.236.57.146:9093,65.60.14.78:9093,65.60.14.134:9093
polite.crawl.consumer.kafka.group.id=polite_crawl_consumer_group_prod
polite.crawl.consumer.max.poll.records=20
polite.crawl.consumer.fetch.min.bytes=1024000
polite.crawl.consumer.fetch.max.bytes=131072000
polite.crawl.consumer.max.partition.fetch.bytes=6553600
polite.crawl.consumer.fetch.max.wait.ms=3000
polite.crawl.consumer.auto.commit=false
polite.crawl.consumer.auto.offset.reset=earliest