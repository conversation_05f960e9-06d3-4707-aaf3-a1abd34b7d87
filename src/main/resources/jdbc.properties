##########################
##    JDBC Setting      ##
##########################

jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.url=***************************************************************************************
jdbc.username=shinetech
jdbc.password=ENC(s/XpougwFrPSZUGeqCPaVcVx5qeZxBkT)

jdbc.external.driver=com.mysql.cj.jdbc.Driver
jdbc.external.url=************************************************************************************************
jdbc.external.username=shinetech
jdbc.external.password=ENC(s/XpougwFrPSZUGeqCPaVcVx5qeZxBkT)

rankcheck.jdbc.driver=com.mysql.cj.jdbc.Driver
rankcheck.jdbc.url=****************************************************************************************************
rankcheck.jdbc.username=shinetech
rankcheck.jdbc.password=ENC(s/XpougwFrPSZUGeqCPaVcVx5qeZxBkT)

rankcheck.external.jdbc.driver=com.mysql.cj.jdbc.Driver
rankcheck.external.jdbc.url=*************************************************************************************************************
rankcheck.external.jdbc.username=shinetech
rankcheck.external.jdbc.password=ENC(s/XpougwFrPSZUGeqCPaVcVx5qeZxBkT)

claritydb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.jdbc.url=*************************************************
claritydb.jdbc.username=default
claritydb.jdbc.password=clarity99!

########################################################################################################################
# mbd010 outer host server11_external 
mdb001.jdbc.driver=com.mysql.cj.jdbc.Driver
mdb001.jdbc.url=************************************************************************************************
mdb001.jdbc.username=root
mdb001.jdbc.password=fixyfoxy

mdb001.actonia.jdbc.driver=com.mysql.cj.jdbc.Driver
mdb001.actonia.jdbc.url=****************************************************************************************
mdb001.actonia.jdbc.username=root
mdb001.actonia.jdbc.password=fixyfoxy

mdb001.actoniamonitor.jdbc.driver=com.mysql.cj.jdbc.Driver
mdb001.actoniamonitor.jdbc.url=************************************************************************************************
mdb001.actoniamonitor.jdbc.username=root
mdb001.actoniamonitor.jdbc.password=fixyfoxy

mdb001.jdbc.external.driver=com.mysql.cj.jdbc.Driver
mdb001.jdbc.external.url=*********************************************************************************************************
mdb001.jdbc.external.username=root
mdb001.jdbc.external.password=fixyfoxy
########################################################################################################################

claritydb.jdbc.dailyqc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.jdbc.dailyqc.url=*********************************************
claritydb.jdbc.dailyqc.username=default
claritydb.jdbc.dailyqc.password=clarity99!

## clickhouse - html crawl
claritydb.crawlurl.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.crawlurl.jdbc.url=******************************************
claritydb.crawlurl.jdbc.username=default
claritydb.crawlurl.jdbc.password=clarity99!

## clickhouse - html crawl
claritydb.politecrawl.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
#claritydb.politecrawl.jdbc.url=*************************************************
claritydb.politecrawl.jdbc.url=*********************************************
claritydb.politecrawl.jdbc.username=default
claritydb.politecrawl.jdbc.password=clarity99!

vedInfo.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
vedInfo.jdbc.url=********************************************
vedInfo.jdbc.username=default
vedInfo.jdbc.password=clarity99!

aurora.jdbc.crawler_manager.driver=com.mysql.cj.jdbc.Driver
aurora.jdbc.crawler_manager.url=******************************************************************************************************************************************************
aurora.jdbc.crawler_manager.username=vedsaver
aurora.jdbc.crawler_manager.password=fixyfoxy

## clickhouse - RG ranking
claritydb.rg.ranking.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.rg.ranking.jdbc.url=******************************************************
claritydb.rg.ranking.jdbc.username=default
claritydb.rg.ranking.jdbc.password=clarity99!

## clickhouse - Content Idea
claritydb.contentidea.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.contentidea.jdbc.url=********************************************************
claritydb.contentidea.jdbc.username=default
claritydb.contentidea.jdbc.password=clarity99!

## clickhouse - ri cold
claritydb.cold.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.cold.jdbc.url=*****************************************************
claritydb.cold.jdbc.username=default
claritydb.cold.jdbc.password=clarity99!

## clickhouse - ri cold2 (cdb-ri-307)
claritydb.ri.cold.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.ri.cold.jdbc.url=*****************************************************
claritydb.ri.cold.jdbc.username=default
claritydb.ri.cold.jdbc.password=clarity99!

## clickhouse - rg cold
claritydb.monthlyranking.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyranking.jdbc.url=****************************************************
claritydb.monthlyranking.jdbc.username=default
claritydb.monthlyranking.jdbc.password=clarity99!

## clickhouse - monthly video ranking - main
claritydb.monthlyvideoranking.rgch.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyvideoranking.rgch.jdbc.url=**********************************************************
claritydb.monthlyvideoranking.rgch.jdbc.username=default
claritydb.monthlyvideoranking.rgch.jdbc.password=clarity99!

## clickhouse - monthly video ranking - backup
claritydb.monthlyvideorankingbackup.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyvideorankingbackup.jdbc.url=**********************************************************
claritydb.monthlyvideorankingbackup.jdbc.username=default
claritydb.monthlyvideorankingbackup.jdbc.password=clarity99!

## clickhouse - monthly video ranking - kfkTest
claritydb.monthlyvideorankingkfk.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyvideorankingkfk.jdbc.url=**********************************************************
claritydb.monthlyvideorankingkfk.jdbc.username=default
claritydb.monthlyvideorankingkfk.jdbc.password=clarity99!

## clickhouse -adhoc hourly
claritydb.adhoc.hourly.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.adhoc.hourly.jdbc.url=**********************************************************
claritydb.adhoc.hourly.jdbc.username=actoniacdb
claritydb.adhoc.hourly.jdbc.password=0%z=Kft99003k#Y6

## clickhouse -adhoc-002 link seeker
claritydb.adhoc.linkSeeker.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.adhoc.linkSeeker.jdbc.url=*******************************************************
claritydb.adhoc.linkSeeker.jdbc.username=actoniacdb
claritydb.adhoc.linkSeeker.jdbc.password=0%z=Kft99003k#Y6

claritydb.gsc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.gsc.jdbc.url=***********************************************************************
claritydb.gsc.jdbc.username=default
claritydb.gsc.jdbc.password=clarity99!

# youTube ranking cdb-ri-307 cold
claritydb.youtube.cold.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.youtube.cold.jdbc.url=******************************************************************************
claritydb.youtube.cold.jdbc.username=default
claritydb.youtube.cold.jdbc.password=clarity99!

# youTube ranking cdb-ri-nj-114 hot
claritydb.youtube.hot.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.youtube.hot.jdbc.url=*********************************************************************************
claritydb.youtube.hot.jdbc.username=default
claritydb.youtube.hot.jdbc.password=clarity99!

# rank_ved_qc cdb-ri-307
claritydb.rankVedQc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.rankVedQc.jdbc.url=***********************************************
claritydb.rankVedQc.jdbc.username=default
claritydb.rankVedQc.jdbc.password=clarity99!

## clickhouse - live search sv - main
claritydb.livesearchsv.rgch.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.livesearchsv.rgch.jdbc.url=*********************************************************
claritydb.livesearchsv.rgch.jdbc.username=default
claritydb.livesearchsv.rgch.jdbc.password=clarity99!

## clickhouse - live search sv - backup
claritydb.livesearchsv.lweb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.livesearchsv.lweb.jdbc.url=*********************************************************
claritydb.livesearchsv.lweb.jdbc.username=default
claritydb.livesearchsv.lweb.jdbc.password=clarity99!

## clickhouse - aio content
aio.content.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
aio.content.jdbc.url=*******************************************
aio.content.jdbc.username=default
aio.content.jdbc.password=clarity99!

# google suggest cdb-ri-nj-114 hot
claritydb.googleSuggest.hot.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.googleSuggest.hot.jdbc.url=*****************************************************
claritydb.googleSuggest.hot.jdbc.username=default
claritydb.googleSuggest.hot.jdbc.password=clarity99!

# google suggest cdb-ri-307 cold
claritydb.googleSuggest.cold.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.googleSuggest.cold.jdbc.url=**************************************************
claritydb.googleSuggest.cold.jdbc.username=default
claritydb.googleSuggest.cold.jdbc.password=clarity99!

# bing web master
claritydb.bingwm.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.bingwm.jdbc.url=***********************************************
claritydb.bingwm.jdbc.username=default
claritydb.bingwm.jdbc.password=clarity99!

# ai scraper cdb-rg-ch-012 hot
claritydb.aiScraper.hot.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.aiScraper.hot.jdbc.url=***********************************************
claritydb.aiScraper.hot.jdbc.username=default
claritydb.aiScraper.hot.jdbc.password=clarity99!

# ai scraper lweb-rg-012 cold
claritydb.aiScraper.cold.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.aiScraper.cold.jdbc.url=***********************************************
claritydb.aiScraper.cold.jdbc.username=default
claritydb.aiScraper.cold.jdbc.password=clarity99!

# pixel v3 cdb-ri-nj-114 hot
claritydb.pixelV3.hot.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.pixelV3.hot.jdbc.url=****************************************************
claritydb.pixelV3.hot.jdbc.username=actoniacdb
claritydb.pixelV3.hot.jdbc.password=0%z=Kft99003k#Y6

# pixel v3 cdb-ri-307 cold
claritydb.pixelV3.cold.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.pixelV3.cold.jdbc.url=*************************************************
claritydb.pixelV3.cold.jdbc.username=default
claritydb.pixelV3.cold.jdbc.password=clarity99!

# url_inspection cdb-gsc21-010
claritydb.urlInspection.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.urlInspection.jdbc.url=**************************************************
claritydb.urlInspection.jdbc.username=default
claritydb.urlInspection.jdbc.password=clarity99!