<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
          http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
          http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
          http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
          http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<context:component-scan base-package="seoclarity.actonia_competitor_summary.upload.politecrawl" />
	<context:component-scan base-package="seoclarity.actonia_competitor_summary.dao.clarityDB.politecrawl" />

	 <context:property-placeholder location="jdbc.properties" />

	<bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
            	<value>classpath:jdbc.properties</value>
                <value>classpath:mailjet.properties</value>
            </list>
        </property>
    </bean>
    
    <bean class="org.jasypt.spring4.properties.EncryptablePropertyPlaceholderConfigurer" id="propertyConfigurer">
		<constructor-arg>
			<bean class="org.jasypt.encryption.pbe.StandardPBEStringEncryptor">
				<property name="config">
					<bean class="org.jasypt.encryption.pbe.config.EnvironmentStringPBEConfig">
						<property name="algorithm" value="PBEWithMD5AndDES" />
						<property name="passwordEnvName" value="PWD_ENCRYPTION" />
					</bean>
				</property>
			</bean>
		</constructor-arg>
		<property name="locations">
			<list>
				<value>classpath:jdbc.properties</value>
				<value>classpath:mailjet.properties</value>
			</list>
		</property>
	</bean>

	<import resource="applicationContext-email.xml"></import>
</beans>