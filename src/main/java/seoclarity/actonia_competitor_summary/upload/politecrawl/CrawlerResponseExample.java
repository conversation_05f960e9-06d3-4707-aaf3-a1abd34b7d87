package seoclarity.actonia_competitor_summary.upload.politecrawl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.UrlMetricsEntity;

import java.util.Date;

/**
 * Example showing how to use the CrawlerResponse.toUrlMetricsEntity() method
 */
public class CrawlerResponseExample {
    
    public static void main(String[] args) {
        // Create a sample CrawlerResponse
        CrawlerResponse crawlerResponse = new CrawlerResponse();
        
        // Set basic fields
        crawlerResponse.setCrawl_date_long(System.currentTimeMillis());
        crawlerResponse.setDomainId(123);
        crawlerResponse.setUrl("https://example.com/page");
        crawlerResponse.setUrlMurmurHash("abc123hash");
        crawlerResponse.setBrowserMurmurHash("browser456hash");
        crawlerResponse.setSumMd5("sum789md5");
        crawlerResponse.setIndicatorsMd5Json("{\"indicator\":\"value\"}");
        crawlerResponse.setCrawlTimestamp(new Date());
        
        // Set JSON fields that will be converted using toJSONString()
        JSONArray alternateLinks = new JSONArray();
        alternateLinks.add("https://example.com/alt1");
        alternateLinks.add("https://example.com/alt2");
        crawlerResponse.setAlternate_links(alternateLinks);
        
        JSONArray h1Tags = new JSONArray();
        h1Tags.add("Main Heading");
        h1Tags.add("Secondary Heading");
        crawlerResponse.setH1(h1Tags);
        
        JSONObject structuredData = new JSONObject();
        structuredData.put("@type", "Article");
        structuredData.put("headline", "Example Article");
        structuredData.put("author", "John Doe");
        crawlerResponse.setStructured_data(structuredData);
        
        // Set Google Gson JsonObject (page_analysis_rule_1_b)
        JsonObject pageAnalysisRule = new JsonObject();
        pageAnalysisRule.addProperty("isIssue", false);
        pageAnalysisRule.addProperty("severity", "low");
        pageAnalysisRule.addProperty("description", "No issues found");
        crawlerResponse.setPage_analysis_rule_1_b(pageAnalysisRule);
        
        // Set other fields
        crawlerResponse.setTitle("Example Page Title");
        crawlerResponse.setDescription("This is an example page description");
        crawlerResponse.setResponse_code(200);
        crawlerResponse.setTitle_length(18);
        crawlerResponse.setCanonical("https://example.com/canonical");
        
        // Convert CrawlerResponse to UrlMetricsEntity
        UrlMetricsEntity entity = crawlerResponse.toUrlMetricsEntity();
        
        // Display results
        System.out.println("=== CrawlerResponse to UrlMetricsEntity Conversion ===");
        System.out.println("Domain ID: " + entity.getDomainId());
        System.out.println("URL: " + entity.getUrl());
        System.out.println("Title: " + entity.getTitle());
        System.out.println("Response Code: " + entity.getResponse_code());
        System.out.println("Crawl Date: " + entity.getCrawlDate());
        System.out.println("Crawl Time: " + entity.getCrawlTime());
        
        System.out.println("\n=== JSON Fields Converted to String ===");
        System.out.println("Alternate Links (JSON Array -> String): " + entity.getAlternate_links());
        System.out.println("H1 Tags (JSON Array -> String): " + entity.getH1());
        System.out.println("Structured Data (JSON Object -> String): " + entity.getStructured_data());
        System.out.println("Page Analysis Rule (Google Gson JsonObject -> String): " + entity.getPage_analysis_results_chg_ind_json());
        
        System.out.println("\n=== Conversion Complete ===");
        System.out.println("All JSON types have been converted to String using toJSONString() method as required.");
    }
}
