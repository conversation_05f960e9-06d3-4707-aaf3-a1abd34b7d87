package seoclarity.actonia_competitor_summary.upload.politecrawl;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl.UrlMetricsEntity;
import seoclarity.actonia_competitor_summary.kafka.clientconstructor.consumer.PoliteCrawlConsumerConstructor;
import seoclarity.actonia_competitor_summary.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.actonia_competitor_summary.utils.SpringBeanFactory;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2025/5/21 17:18
 */
public class UploadPoliteCrawlToCDB {

    private final Logger log = LogManager.getLogger(UploadPoliteCrawlToCDB.class.getName());
    public static final Gson gson = new Gson();

    public final int threadCount;
    public final int batchSize;
    private static final String TOPIC_POLITE_CRAWL = "polite_crawl";
    public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
    protected static LinkedBlockingQueue<UrlMetricsEntity> urlMetricsEntityLinkedBlockingQueue = new LinkedBlockingQueue<>();
    protected final PoliteCrawlDBService politeCrawlDBService;

    public UploadPoliteCrawlToCDB(int threadCount, int batchSize) {
        this.threadCount = threadCount;
        this.batchSize = batchSize;
        politeCrawlDBService = SpringBeanFactory.getBean("politeCrawlDBService");
    }

    public static void main(String[] args) {
        final int defaultBatchSize = 10;
        int threadCount = 1;
        int batchSize = defaultBatchSize;
        if (args.length == 1) {
            threadCount = Integer.parseInt(args[0]);
            batchSize = defaultBatchSize * threadCount;
        } else if (args.length >= 1) {
            threadCount = Integer.parseInt(args[0]);
            batchSize = Integer.parseInt(args[1]) * threadCount;
        }
        UploadPoliteCrawlToCDB uploadPoliteCrawlToCDB = new UploadPoliteCrawlToCDB(threadCount, batchSize);
        uploadPoliteCrawlToCDB.start();
    }

    private void start() {
        log.info("Start");
        log.info("Init threadPool");
        threadPool.init();
        log.info("Start execute");
        startExecute();
        log.info("Close threadPool");
        threadPool.destroy();
        log.info("End");
    }

    private void startExecute() {
        try (Consumer<String, String> consumer = PoliteCrawlConsumerConstructor.getInstance().buildKafkaConsumerWithTopics(Collections.singleton(TOPIC_POLITE_CRAWL))) {
            List<String> resultList = new ArrayList<>();
            long count = 0L;
            int number = 0;
            int total = 100;
            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(5000));
                if (records.count() == 0) {
                    if (number++ > total) {
                        break;
                    }
                    continue;
                }
                count += records.count();
                records.forEach(record -> resultList.add(record.value()));
                if (resultList.size() >= batchSize) {
                    testToDB(resultList);
//                    listToDB(resultList);
                    consumer.commitSync();
                    resultList.clear();
                    return;
                }
            }
            if (!resultList.isEmpty()) {
                listToDB(resultList);
                consumer.commitSync();
                resultList.clear();
            }
            log.info("Total count message: " + count);
        } catch (Exception e) {
            log.error("Kafka connection timed out. Exception: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void testToDB(List<String> resultList) {
        resultList.forEach(item -> {
            JSONObject jsonObject = null;
            try {
                jsonObject = JSONObject.parseObject(item);
            } catch (Exception e) {
                log.error("JSON parse error. Exception: " + e.getMessage());
                log.error("value: " + item);
                e.printStackTrace();
            }
            if (jsonObject == null) {
                return;
            }
            String lastSumMd5 = jsonObject.getString("lastSumMd5");
            final String crawlerResponse = jsonObject.getString("crawlerResponse");
            if (StringUtils.isBlank(lastSumMd5)) {
                final UrlMetricsEntity urlMetricsEntity = UploadPoliteCrawlToCDB.gson.fromJson(crawlerResponse, UrlMetricsEntity.class);
                UploadPoliteCrawlToCDB.urlMetricsEntityLinkedBlockingQueue.add(urlMetricsEntity);
                if (UploadPoliteCrawlToCDB.urlMetricsEntityLinkedBlockingQueue.size() > 10) {
                    // batch insert
//                    this.politeCrawlDBService.batchInsertUrlMetrics();
                }
            } else {
                // TODO 查询不同
                final JSONObject currMd5 = jsonObject.getJSONObject("currMd5");
                final JSONObject lastMd5 = jsonObject.getJSONObject("lastMd5");

                // find diff value for each key between lastMd5 and currMd5
                currMd5.entrySet().stream().filter(entry -> !lastMd5.containsKey(entry.getKey()))
                        .map(entry -> {
                            final String key = entry.getKey();
                            return key;
                        });
            }
        });
    }


    private void listToDB(List<String> resultList) {
        int partSize = (int) Math.ceil((double) resultList.size() / threadCount);
        List<List<String>> spiltResultList = IntStream.range(0, threadCount)
                .mapToObj(i -> resultList.stream().skip((long) i * partSize).limit(partSize)
                        .collect(Collectors.toList())).collect(Collectors.toList());
        int i = 0;
        for (List<String> item : spiltResultList) {
            while (true) {
                try {
                    PoliteCrawlCommand politeCrawlCommand = new PoliteCrawlCommand(true, ++i, item);
                    threadPool.execute(politeCrawlCommand);
                    break;
                } catch (Exception e) {
                    log.error("Thread: " + i + " Call exception. Exception: " + e.getMessage());
                }
            }
        }
        do {
            try {
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("Sleep exception. Exception: " + e.getMessage());
                e.printStackTrace();
            }
        } while (threadPool.getThreadPool().getActiveCount() > 0);
    }

}
