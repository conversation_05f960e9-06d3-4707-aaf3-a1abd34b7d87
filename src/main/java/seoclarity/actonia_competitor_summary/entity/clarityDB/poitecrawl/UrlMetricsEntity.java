package seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class UrlMetricsEntity {

	private LocalDate crawlDate;
	private LocalDateTime crawlTime;
	private Integer domainId;
	private String url;
	private String urlMurmurHash;
	private String browserMurmurHash;
	private String sumMd5;
	private String indicatorsMd5Json;

	private String urlDomain;
	private Integer weekOfYear;
	private String lowerCaseUrlHash;
	private String changeTrackingHash;
	private Date crawlTimestamp;
	private Date previousCrawlTimestamp;
	private String alternate_links;
	private Integer amphtml_flag;
	private String amphtml_href;
	private String analyzed_url_flg_s;
	private String analyzed_url_s;
	private String archive_flg;
	private String archive_flg_x_tag;
	private String blocked_by_robots;
	private String canonical;
	private Integer canonical_flg;
	private Integer canonical_header_flag;
	private String canonical_header_type;
	private String canonical_type;
	private String canonical_url_is_consistent;
	private String content_type;
	private String custom_data;
	private String description;
	private Integer description_flg;
	private Integer description_length;
	private Integer final_response_code;
	private Integer follow_flg;
	private String follow_flg_x_tag;
	private String h1;
	private Integer h1_count;
	private String h1_flg;
	private Integer h1_length;
	private String h2;
	private Integer header_noarchive;
	private Integer header_nofollow;
	private Integer header_noindex;
	private Integer header_noodp;
	private Integer header_nosnippet;
	private Integer header_noydir;
	private String hreflang_errors;
	private String hreflang_links;
	private Integer hreflang_links_out_count;
	private Integer hreflang_url_count;
	private String index_flg;
	private String index_flg_x_tag;
	private Integer indexable;
	private String insecure_resources;
	private Integer insecure_resources_flag;
	private Integer long_redirect;
	private String meta_charset;
	private String meta_content_type;
	private Integer meta_disabled_sitelinks;
	private Integer meta_noodp;
	private Integer meta_nosnippet;
	private Integer meta_noydir;
	private Integer meta_redirect;
	private Integer mixed_redirects;
	private Integer mobile_rel_alternate_url_is_consistent;
	private Integer noodp;
	private Integer nosnippet;
	private Integer noydir;
	private String og_markup;
	private Integer og_markup_flag;
	private Integer og_markup_length;
	private Integer outlink_count;
	private String page_analysis_results;
	private String page_link;
	private Integer page_timeout_flag;
	private Integer paginated;
	private String pagination_links;
	private String protocol;
	private Integer redirect_blocked;
	private String redirect_blocked_reason;
	private String redirect_chain;
	private String redirect_final_url;
	private Integer redirect_flg;
	private Integer redirect_times;
	private String rel_next_html_url;
	private Integer rel_next_url_is_consistent;
	private Integer rel_prev_url_is_consistent;
	private String request_headers;
	private String request_time;
	private Integer response_code;
	private String response_headers;
	private Integer retry_attempted;
	private String robots;
	private String robots_contents;
	private Integer robots_contents_x_tag;
	private Integer robots_flg;
	private String robots_flg_x_tag;
	private String source_url;
	private String splash_took;
	private String structured_data;
	private String title;
	private Integer title_flg;
	private Integer title_length;
	private String title_md5;
	private String title_simhash;
	private Integer twitter_description_length;
	private String twitter_markup;
	private Integer twitter_markup_flag;
	private Integer twitter_markup_length;
	private Integer url_length;
	private String valid_twitter_card;
	private String viewport_content;
	private Integer viewport_flag;
	private String file_name;
	private String base_tag;
	private Integer base_tag_flag;
	private String base_tag_target;
	private String page_analysis_results_chg_ind_json;

}