package seoclarity.actonia_competitor_summary.entity.clarityDB.poitecrawl;

import lombok.Data;
import seoclarity.actonia_competitor_summary.upload.politecrawl.CrawlerResponse;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
public class UrlMetricsEntity {

	private LocalDate crawlDate;
	private LocalDateTime crawlTime;
	private Integer domainId;
	private String url;
	private String urlMurmurHash;
	private String browserMurmurHash;
	private String sumMd5;
	private String indicatorsMd5Json;

	private String urlDomain;
	private Integer weekOfYear;
	private String lowerCaseUrlHash;
	private String changeTrackingHash;
	private Date crawlTimestamp;
	private Date previousCrawlTimestamp;
	private String alternate_links;
	private Integer amphtml_flag;
	private String amphtml_href;
	private String analyzed_url_flg_s;
	private String analyzed_url_s;
	private String archive_flg;
	private String archive_flg_x_tag;
	private String blocked_by_robots;
	private String canonical;
	private Integer canonical_flg;
	private Integer canonical_header_flag;
	private String canonical_header_type;
	private String canonical_type;
	private String canonical_url_is_consistent;
	private String content_type;
	private String custom_data;
	private String description;
	private Integer description_flg;
	private Integer description_length;
	private Integer final_response_code;
	private Integer follow_flg;
	private String follow_flg_x_tag;
	private String h1;
	private Integer h1_count;
	private String h1_flg;
	private Integer h1_length;
	private String h2;
	private Integer header_noarchive;
	private Integer header_nofollow;
	private Integer header_noindex;
	private Integer header_noodp;
	private Integer header_nosnippet;
	private Integer header_noydir;
	private String hreflang_errors;
	private String hreflang_links;
	private Integer hreflang_links_out_count;
	private Integer hreflang_url_count;
	private String index_flg;
	private String index_flg_x_tag;
	private Integer indexable;
	private String insecure_resources;
	private Integer insecure_resources_flag;
	private Integer long_redirect;
	private String meta_charset;
	private String meta_content_type;
	private Integer meta_disabled_sitelinks;
	private Integer meta_noodp;
	private Integer meta_nosnippet;
	private Integer meta_noydir;
	private Integer meta_redirect;
	private Integer mixed_redirects;
	private Integer mobile_rel_alternate_url_is_consistent;
	private Integer noodp;
	private Integer nosnippet;
	private Integer noydir;
	private String og_markup;
	private Integer og_markup_flag;
	private Integer og_markup_length;
	private Integer outlink_count;
	private String page_analysis_results;
	private String page_link;
	private Integer page_timeout_flag;
	private Integer paginated;
	private String pagination_links;
	private String protocol;
	private Integer redirect_blocked;
	private String redirect_blocked_reason;
	private String redirect_chain;
	private String redirect_final_url;
	private Integer redirect_flg;
	private Integer redirect_times;
	private String rel_next_html_url;
	private Integer rel_next_url_is_consistent;
	private Integer rel_prev_url_is_consistent;
	private String request_headers;
	private String request_time;
	private Integer response_code;
	private String response_headers;
	private Integer retry_attempted;
	private String robots;
	private String robots_contents;
	private Integer robots_contents_x_tag;
	private Integer robots_flg;
	private String robots_flg_x_tag;
	private String source_url;
	private String splash_took;
	private String structured_data;
	private String title;
	private Integer title_flg;
	private Integer title_length;
	private String title_md5;
	private String title_simhash;
	private Integer twitter_description_length;
	private String twitter_markup;
	private Integer twitter_markup_flag;
	private Integer twitter_markup_length;
	private Integer url_length;
	private String valid_twitter_card;
	private String viewport_content;
	private Integer viewport_flag;
	private String file_name;
	private String base_tag;
	private Integer base_tag_flag;
	private String base_tag_target;
	private String page_analysis_results_chg_ind_json;

	public static UrlMetricsEntity fromCrawlerResponse(CrawlerResponse crawlerResponse) {
		UrlMetricsEntity entity = new UrlMetricsEntity();

		// Basic fields
		entity.setDomainId(this.domainId);
		entity.setUrl(this.url);
		entity.setUrlMurmurHash(this.urlMurmurHash);
		entity.setBrowserMurmurHash(this.browserMurmurHash);
		entity.setSumMd5(this.sumMd5);
		entity.setIndicatorsMd5Json(this.indicatorsMd5Json);

		// Date conversions
		if (this.crawl_date_long != null) {
			entity.setCrawlDate(Instant.ofEpochMilli(this.crawl_date_long)
					.atZone(ZoneId.systemDefault()).toLocalDate());
		}
		if (this.crawlTimestamp != null) {
			entity.setCrawlTime(this.crawlTimestamp.toInstant()
					.atZone(ZoneId.systemDefault()).toLocalDateTime());
		}
		entity.setCrawlTimestamp(this.crawlTimestamp);
		entity.setPreviousCrawlTimestamp(this.previousCrawlTimestamp);

		// String fields
		entity.setUrlDomain(this.urlDomain);
		entity.setWeekOfYear(this.weekOfYear);
		entity.setLowerCaseUrlHash(this.lowerCaseUrlHash);
		entity.setChangeTrackingHash(this.changeTrackingHash);
		entity.setAmphtml_href(this.amphtml_href);
		entity.setAnalyzed_url_flg_s(this.analyzed_url_flg_s);
		entity.setAnalyzed_url_s(this.analyzed_url_s);
		entity.setArchive_flg(this.archive_flg);
		entity.setArchive_flg_x_tag(this.archive_flg_x_tag);
		entity.setBlocked_by_robots(this.blocked_by_robots);
		entity.setCanonical(this.canonical);
		entity.setCanonical_header_type(this.canonical_header_type);
		entity.setCanonical_type(this.canonical_type);
		entity.setCanonical_url_is_consistent(this.canonical_url_is_consistent);
		entity.setContent_type(this.content_type);
		entity.setDescription(this.description);
		entity.setFollow_flg_x_tag(this.follow_flg_x_tag);
		entity.setH1_flg(this.h1_flg);
		entity.setHreflang_errors(this.hreflang_errors);
		entity.setIndex_flg(this.index_flg);
		entity.setIndex_flg_x_tag(this.index_flg_x_tag);
		entity.setMeta_charset(this.meta_charset);
		entity.setMeta_content_type(this.meta_content_type);
		entity.setProtocol(this.protocol);
		entity.setRedirect_blocked_reason(this.redirect_blocked_reason);
		entity.setRedirect_chain(this.redirect_chain);
		entity.setRedirect_final_url(this.redirect_final_url);
		entity.setRel_next_html_url(this.rel_next_html_url);
		entity.setRequest_headers(this.request_headers);
		entity.setRequest_time(this.request_time);
		entity.setRobots(this.robots);
		entity.setRobots_contents(this.robots_contents);
		entity.setRobots_flg_x_tag(this.robots_flg_x_tag);
		entity.setSource_url(this.source_url);
		entity.setSplash_took(this.splash_took);
		entity.setTitle(this.title);
		entity.setTitle_simhash(this.title_simhash);
		entity.setTwitter_markup(this.twitter_markup);
		entity.setValid_twitter_card(this.valid_twitter_card);
		entity.setViewport_content(this.viewport_content);
		entity.setFile_name(this.file_name);
		entity.setBase_tag(this.base_tag);
		entity.setBase_tag_target(this.base_tag_target);
		entity.setPage_analysis_results(this.page_analysis_results);
		entity.setPage_analysis_results_chg_ind_json(this.page_analysis_results_chg_ind_json);

		// Integer fields
		entity.setAmphtml_flag(this.amphtml_flag);
		entity.setCanonical_flg(this.canonical_flg);
		entity.setCanonical_header_flag(this.canonical_header_flag);
		entity.setDescription_flg(this.description_flg);
		entity.setDescription_length(this.description_length);
		entity.setFinal_response_code(this.final_response_code);
		entity.setFollow_flg(this.follow_flg);
		entity.setH1_count(this.h1_count);
		entity.setH1_length(this.h1_length);
		entity.setHeader_noarchive(this.header_noarchive);
		entity.setHeader_nofollow(this.header_nofollow);
		entity.setHeader_noindex(this.header_noindex);
		entity.setHeader_noodp(this.header_noodp);
		entity.setHeader_nosnippet(this.header_nosnippet);
		entity.setHeader_noydir(this.header_noydir);
		entity.setHreflang_links_out_count(this.hreflang_links_out_count);
		entity.setHreflang_url_count(this.hreflang_url_count);
		entity.setIndexable(this.indexable);
		entity.setInsecure_resources_flag(this.insecure_resources_flag);
		entity.setLong_redirect(this.long_redirect);
		entity.setMeta_disabled_sitelinks(this.meta_disabled_sitelinks);
		entity.setMeta_noodp(this.meta_noodp);
		entity.setMeta_nosnippet(this.meta_nosnippet);
		entity.setMeta_noydir(this.meta_noydir);
		entity.setMeta_redirect(this.meta_redirect);
		entity.setMixed_redirects(this.mixed_redirects);
		entity.setMobile_rel_alternate_url_is_consistent(this.mobile_rel_alternate_url_is_consistent);
		entity.setNoodp(this.noodp);
		entity.setNosnippet(this.nosnippet);
		entity.setNoydir(this.noydir);
		entity.setOg_markup_flag(this.og_markup_flag);
		entity.setOg_markup_length(this.og_markup_length);
		entity.setOutlink_count(this.outlink_count);
		// Note: page_timeout_flag field doesn't exist in CrawlerResponse, so we skip it
		entity.setPaginated(this.paginated);
		entity.setRedirect_blocked(this.redirect_blocked);
		entity.setRedirect_flg(this.redirect_flg);
		entity.setRedirect_times(this.redirect_times);
		entity.setRel_next_url_is_consistent(this.rel_next_url_is_consistent);
		entity.setRel_prev_url_is_consistent(this.rel_prev_url_is_consistent);
		entity.setResponse_code(this.response_code);
		entity.setRetry_attempted(this.retry_attempted);
		entity.setRobots_contents_x_tag(this.robots_contents_x_tag);
		entity.setRobots_flg(this.robots_flg);
		entity.setTitle_flg(this.title_flg);
		entity.setTitle_length(this.title_length);
		// Note: title_md5 field is commented out in CrawlerResponse, so we skip it
		entity.setTwitter_description_length(this.twitter_description_length);
		entity.setTwitter_markup_flag(this.twitter_markup_flag);
		entity.setTwitter_markup_length(this.twitter_markup_length);
		entity.setUrl_length(this.url_length);
		entity.setViewport_flag(this.viewport_flag);
		entity.setBase_tag_flag(this.base_tag_flag);

		// JSON fields converted to String using toJSONString()
		entity.setAlternate_links(this.alternate_links != null ? this.alternate_links.toJSONString() : null);
		entity.setCustom_data(this.custom_data != null ? this.custom_data.toJSONString() : null);
		entity.setH1(this.h1 != null ? this.h1.toJSONString() : null);
		entity.setH2(this.h2 != null ? this.h2.toJSONString() : null);
		entity.setHreflang_links(this.hreflang_links != null ? this.hreflang_links.toJSONString() : null);
		entity.setInsecure_resources(this.insecure_resources != null ? this.insecure_resources.toJSONString() : null);
		entity.setOg_markup(this.og_markup != null ? this.og_markup.toJSONString() : null);
		entity.setPage_link(this.page_link != null ? this.page_link.toJSONString() : null);
		entity.setPagination_links(this.pagination_links != null ? this.pagination_links.toJSONString() : null);
		entity.setResponse_headers(this.response_headers != null ? this.response_headers.toJSONString() : null);
		entity.setStructured_data(this.structured_data != null ? this.structured_data.toJSONString() : null);

		// Handle Google Gson JsonObject (page_analysis_rule_1_b) - convert to String using toString()
		if (this.page_analysis_rule_1_b != null) {
			entity.setPage_analysis_results_chg_ind_json(this.page_analysis_rule_1_b.toString());
		}

		return entity;
	}
}